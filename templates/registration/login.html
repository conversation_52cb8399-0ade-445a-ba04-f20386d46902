{% extends 'base.html' %}
{% load static %}
{% load widget_tweaks %}

{% block title %}Login - IMT Insurance Claims{% endblock %}

{% block content %}
<div class="row justify-content-center">
    <div class="col-md-6 col-lg-4">
        <div class="card">
            <div class="card-header text-center">
                <h4 class="mb-0">
                    <i class="fas fa-shield-alt me-2"></i>IMT Insurance
                </h4>
                <p class="text-muted mb-0">Claims System Login</p>
            </div>
            <div class="card-body">
                {% if form.errors %}
                    <div class="alert alert-danger">
                        <i class="fas fa-exclamation-triangle me-2"></i>
                        Please correct the errors below.
                    </div>
                {% endif %}

                <form method="post">
                    {% csrf_token %}
                    <div class="mb-3">
                        <label for="{{ form.username.id_for_label }}" class="form-label">
                            <i class="fas fa-user me-1"></i>Username
                        </label>
                        <input type="text" name="username" class="form-control" id="{{ form.username.id_for_label }}" required>
                        {% if form.username.errors %}
                            <div class="text-danger small mt-1">
                                {{ form.username.errors.0 }}
                            </div>
                        {% endif %}
                    </div>

                    <div class="mb-3">
                        <label for="{{ form.password.id_for_label }}" class="form-label">
                            <i class="fas fa-lock me-1"></i>Password
                        </label>
                        <input type="password" name="password" class="form-control" id="{{ form.password.id_for_label }}" required>
                        {% if form.password.errors %}
                            <div class="text-danger small mt-1">
                                {{ form.password.errors.0 }}
                            </div>
                        {% endif %}
                    </div>

                    <div class="d-grid">
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-sign-in-alt me-2"></i>Login
                        </button>
                    </div>
                </form>
            </div>
            <div class="card-footer text-center">
                <small class="text-muted">
                    Need help? Contact your IMT agent or call
                    <a href="tel:+***********">800‑274‑3531</a>
                </small>
            </div>
        </div>

        <!-- Demo Accounts Info -->
        <div class="card mt-4">
            <div class="card-header">
                <h6 class="mb-0">
                    <i class="fas fa-info-circle me-2"></i>Demo Test Accounts
                </h6>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6 mb-3">
                        <strong class="text-primary">👑 Admin Accounts:</strong><br>
                        <small class="text-muted">Full system access</small><br>
                        <button type="button" class="btn btn-sm btn-outline-primary me-1 mb-1" onclick="quickLogin('admin', 'admin123')">
                            admin
                        </button>
                    </div>
                    <div class="col-md-6 mb-3">
                        <strong class="text-success">🔍 Claims Adjusters:</strong><br>
                        <small class="text-muted">Process and review claims</small><br>
                        <button type="button" class="btn btn-sm btn-outline-success me-1 mb-1" onclick="quickLogin('adjuster_barbara_rodriguez', 'adjuster123')">
                            adjuster_barbara_rodriguez
                        </button>
                        <button type="button" class="btn btn-sm btn-outline-success mb-1" onclick="quickLogin('adjuster_matthew_anderson', 'adjuster123')">
                            adjuster_matthew_anderson
                        </button>
                    </div>
                </div>
                <div class="row">
                    <div class="col-md-6 mb-3">
                        <strong class="text-info">👤 Customer Accounts:</strong><br>
                        <small class="text-muted">Submit and track claims</small><br>
                        <button type="button" class="btn btn-sm btn-outline-info me-1 mb-1" onclick="quickLogin('patricia.garcia44', 'customer123')">
                            patricia.garcia44
                        </button>
                        <button type="button" class="btn btn-sm btn-outline-info mb-1" onclick="quickLogin('nancy.ramirez10', 'customer123')">
                            nancy.ramirez10
                        </button>
                    </div>
                    <div class="col-md-6 mb-3">
                        <strong class="text-warning">🏢 Insurance Agents:</strong><br>
                        <small class="text-muted">Assist customers</small><br>
                        <button type="button" class="btn btn-sm btn-outline-warning me-1 mb-1" onclick="quickLogin('agent_betty_martin', 'agent123')">
                            agent_betty_martin
                        </button>
                        <button type="button" class="btn btn-sm btn-outline-warning mb-1" onclick="quickLogin('agent_charles_white', 'agent123')">
                            agent_charles_white
                        </button>
                    </div>
                </div>
                <div class="text-center mt-2">
                    <small class="text-muted">
                        <i class="fas fa-flask me-1"></i>
                        Development Environment - Pre-seeded with sample data
                    </small>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
function quickLogin(username, password) {
    // Fill in the form fields
    document.querySelector('input[name="username"]').value = username;
    document.querySelector('input[name="password"]').value = password;
    
    // Add visual feedback
    const form = document.querySelector('form');
    const originalButton = document.querySelector('button[type="submit"]');
    
    // Change button to show loading state
    originalButton.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>Logging in...';
    originalButton.disabled = true;
    
    // Submit the form after a short delay for visual feedback
    setTimeout(() => {
        form.submit();
    }, 500);
}

// Add hover effect to demo buttons
document.addEventListener('DOMContentLoaded', function() {
    const demoButtons = document.querySelectorAll('.btn-outline-primary, .btn-outline-success, .btn-outline-info, .btn-outline-warning');
    demoButtons.forEach(button => {
        button.addEventListener('mouseenter', function() {
            this.style.transform = 'scale(1.05)';
        });
        button.addEventListener('mouseleave', function() {
            this.style.transform = 'scale(1)';
        });
    });
});
</script>
{% endblock %}
