"""
Management command to check CSRF settings
"""
from django.core.management.base import BaseCommand
from django.conf import settings
import os


class Command(BaseCommand):
    help = 'Check CSRF and security settings'

    def handle(self, *args, **options):
        self.stdout.write(self.style.SUCCESS('=== Django CSRF Settings Check ==='))
        
        # Basic settings
        self.stdout.write(f"DJANGO_SETTINGS_MODULE: {os.environ.get('DJANGO_SETTINGS_MODULE')}")
        self.stdout.write(f"DEBUG: {settings.DEBUG}")
        self.stdout.write(f"ALLOWED_HOSTS: {settings.ALLOWED_HOSTS}")
        
        # CSRF settings
        self.stdout.write(f"\nCSRF_TRUSTED_ORIGINS: {getattr(settings, 'CSRF_TRUSTED_ORIGINS', 'NOT SET')}")
        self.stdout.write(f"CSRF_COOKIE_SECURE: {settings.CSRF_COOKIE_SECURE}")
        self.stdout.write(f"CSRF_COOKIE_HTTPONLY: {getattr(settings, 'CSRF_COOKIE_HTTPONLY', 'NOT SET')}")
        self.stdout.write(f"CSRF_USE_SESSIONS: {getattr(settings, 'CSRF_USE_SESSIONS', False)}")
        
        # Security settings
        self.stdout.write(f"\nSECURE_PROXY_SSL_HEADER: {getattr(settings, 'SECURE_PROXY_SSL_HEADER', 'NOT SET')}")
        self.stdout.write(f"USE_X_FORWARDED_HOST: {getattr(settings, 'USE_X_FORWARDED_HOST', False)}")
        self.stdout.write(f"USE_X_FORWARDED_PORT: {getattr(settings, 'USE_X_FORWARDED_PORT', False)}")
        
        # Environment variables
        self.stdout.write(self.style.SUCCESS('\n=== Environment Variables ==='))
        self.stdout.write(f"ALLOWED_HOSTS env: {os.environ.get('ALLOWED_HOSTS', 'NOT SET')}")
        self.stdout.write(f"CSRF_TRUSTED_ORIGINS env: {os.environ.get('CSRF_TRUSTED_ORIGINS', 'NOT SET')}")
        
        # Middleware check
        self.stdout.write(self.style.SUCCESS('\n=== Middleware Check ==='))
        csrf_middleware = 'django.middleware.csrf.CsrfViewMiddleware'
        if csrf_middleware in settings.MIDDLEWARE:
            self.stdout.write(self.style.SUCCESS(f"✓ {csrf_middleware} is enabled"))
        else:
            self.stdout.write(self.style.ERROR(f"✗ {csrf_middleware} is NOT enabled"))