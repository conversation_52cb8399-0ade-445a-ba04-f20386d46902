from django.urls import path
from . import views

app_name = 'claims'

urlpatterns = [
    # Dashboard
    path('', views.dashboard, name='dashboard'),
    path('dashboard/', views.dashboard, name='dashboard_alt'),

    # Claim management
    path('submit/', views.submit_claim, name='submit'),
    path('list/', views.claim_list, name='list'),
    path('my-claims/', views.my_claims, name='my_claims'),
    path('claim/<int:claim_id>/', views.claim_detail, name='detail'),
    path('claim/<int:claim_id>/edit/', views.edit_claim, name='edit'),

    # Notes
    path('claim/<int:claim_id>/add-note/', views.add_note, name='add_note'),

    # Tasks
    path('tasks/', views.task_list, name='tasks'),
    path('tasks/<int:task_id>/', views.task_detail, name='task_detail'),
    path('tasks/<int:task_id>/complete/', views.complete_task, name='complete_task'),
    path('claim/<int:claim_id>/add-task/', views.add_task, name='add_task'),
    
    # Test error view (development only)
    path('test-error/', views.test_error, name='test_error'),
]
