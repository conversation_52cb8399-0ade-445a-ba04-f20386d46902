#!/bin/bash
# Deploy script for Kubernetes development environment

set -e

echo "🚀 IMT Insurance Claims - Kubernetes Development Deployment"
echo "========================================================="

# Check if kubectl is installed
if ! command -v kubectl &> /dev/null; then
    echo "❌ kubectl is not installed. Please install kubectl first."
    exit 1
fi

# Check if Docker image exists
if ! docker image inspect demo-claims-web:latest &> /dev/null; then
    echo "📦 Building Docker image..."
    docker build -t demo-claims-web:latest .
fi

# Function to wait for pod readiness
wait_for_pod() {
    local label=$1
    local namespace=$2
    echo "⏳ Waiting for $label pods to be ready..."
    kubectl wait --for=condition=ready pod -l app=$label -n $namespace --timeout=300s
}

# Deploy to Kubernetes
echo ""
echo "📋 Deploying to Kubernetes..."
kubectl apply -f k8s-dev-all-in-one.yaml

# Wait for namespace to be created
sleep 2

# Wait for services to be ready
echo ""
echo "⏳ Waiting for services to start..."
wait_for_pod "postgres" "imt-dev"
wait_for_pod "redis" "imt-dev"
wait_for_pod "web" "imt-dev"
wait_for_pod "nginx" "imt-dev"

# Get service information
echo ""
echo "✅ Deployment complete!"
echo ""
echo "📊 Service Status:"
kubectl get pods -n imt-dev
echo ""
kubectl get services -n imt-dev

# Get access information
NODE_PORT=$(kubectl get service nginx-service -n imt-dev -o jsonpath='{.spec.ports[0].nodePort}')
echo ""
echo "🌐 Access Information:"
echo "===================="
echo "Development URL: http://localhost:${NODE_PORT}"
echo "Django Admin: http://localhost:${NODE_PORT}/admin"
echo ""
echo "📝 Default Credentials:"
echo "Admin: admin / admin123"
echo "Demo Admin: imtadmin / imtadmin123"
echo ""
echo "🔧 Useful Commands:"
echo "View logs: kubectl logs -f deployment/web -n imt-dev"
echo "Shell access: kubectl exec -it deployment/web -n imt-dev -- /bin/bash"
echo "Delete deployment: kubectl delete -f k8s-dev-all-in-one.yaml"
echo ""

# Port forwarding option
echo "💡 For direct access without NodePort, you can use port forwarding:"
echo "kubectl port-forward service/nginx-service 8080:80 -n imt-dev"
echo "Then access at: http://localhost:8080"