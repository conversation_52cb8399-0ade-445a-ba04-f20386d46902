#!/usr/bin/env python
"""
Quick diagnostic script to check CSRF settings
"""
import os
import sys

# Set Django settings module
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'claims_system.settings.development')

# Setup Django
import django
django.setup()

from django.conf import settings

print("=== Django CSRF Settings Debug ===")
print(f"DJANGO_SETTINGS_MODULE: {os.environ.get('DJANGO_SETTINGS_MODULE')}")
print(f"DEBUG: {settings.DEBUG}")
print(f"ALLOWED_HOSTS: {settings.ALLOWED_HOSTS}")
print(f"CSRF_TRUSTED_ORIGINS: {getattr(settings, 'CSRF_TRUSTED_ORIGINS', 'NOT SET')}")
print(f"CSRF_COOKIE_SECURE: {settings.CSRF_COOKIE_SECURE}")
print(f"SESSION_COOKIE_SECURE: {settings.SESSION_COOKIE_SECURE}")
print(f"SECURE_PROXY_SSL_HEADER: {getattr(settings, 'SECURE_PROXY_SSL_HEADER', 'NOT SET')}")

# Check environment variables
print("\n=== Environment Variables ===")
print(f"ALLOWED_HOSTS env: {os.environ.get('ALLOWED_HOSTS', 'NOT SET')}")
print(f"CSRF_TRUSTED_ORIGINS env: {os.environ.get('CSRF_TRUSTED_ORIGINS', 'NOT SET')}")

# Test parsing
from decouple import config
print("\n=== Config Parsing Test ===")
csrf_env = config('CSRF_TRUSTED_ORIGINS', default='https://demo.imtins.com,http://demo.imtins.com')
print(f"Raw CSRF_TRUSTED_ORIGINS: {csrf_env}")
parsed = [s.strip() for s in csrf_env.split(',') if s.strip()]
print(f"Parsed CSRF_TRUSTED_ORIGINS: {parsed}")