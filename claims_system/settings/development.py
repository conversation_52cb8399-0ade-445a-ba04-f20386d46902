"""
Development settings for claims_system project.
"""

from .base import *
from decouple import config

# Development-specific settings
DEBUG = True

# ALLOWED_HOSTS is configured in base.py from environment variable
# For development, we can override to allow all hosts if needed
allowed_hosts_env = config('ALLOWED_HOSTS', default='*')
if allowed_hosts_env == '*':
    ALLOWED_HOSTS = ['*']
else:
    ALLOWED_HOSTS = [s.strip() for s in allowed_hosts_env.split(',') if s.strip()]

# Database for development (SQLite)
DATABASES = {
    'default': {
        'ENGINE': 'django.db.backends.sqlite3',
        'NAME': BASE_DIR / 'db.sqlite3',
    }
}

# Email backend for development
EMAIL_BACKEND = 'django.core.mail.backends.console.EmailBackend'

# Static files for development
STATICFILES_STORAGE = 'django.contrib.staticfiles.storage.StaticFilesStorage'

# CSRF Trusted Origins for development
# This inherits from base.py, but we can add development-specific origins if needed
csrf_trusted = config('CSRF_TRUSTED_ORIGINS', default='', cast=lambda v: [s.strip() for s in v.split(',') if s.strip()])
if csrf_trusted:
    CSRF_TRUSTED_ORIGINS = csrf_trusted
else:
    # Default trusted origins for development
    CSRF_TRUSTED_ORIGINS = [
        'http://localhost:8000',
        'http://127.0.0.1:8000',
        'http://localhost',
        'http://127.0.0.1',
        'https://demo.imtins.com',
        'http://demo.imtins.com',
        'http://*************',
        'https://*************',
    ]

# Security settings for development
SECURE_SSL_REDIRECT = False
SECURE_HSTS_SECONDS = 0
SECURE_HSTS_INCLUDE_SUBDOMAINS = False
SECURE_HSTS_PRELOAD = False
SECURE_CONTENT_TYPE_NOSNIFF = False
SECURE_BROWSER_XSS_FILTER = False
SESSION_COOKIE_SECURE = False
CSRF_COOKIE_SECURE = False

# Override any SSL redirects from base settings
SECURE_PROXY_SSL_HEADER = None

# Debug: Print CSRF settings on startup
import sys
if 'runserver' in sys.argv or 'gunicorn' in sys.argv[0]:
    print(f"[DEVELOPMENT] ALLOWED_HOSTS: {ALLOWED_HOSTS}")
    print(f"[DEVELOPMENT] CSRF_TRUSTED_ORIGINS: {CSRF_TRUSTED_ORIGINS}")

# Logging configuration for development
LOGGING = {
    'version': 1,
    'disable_existing_loggers': False,
    'formatters': {
        'verbose': {
            'format': '[{levelname}] {asctime} {module} {process:d} {thread:d} {message}',
            'style': '{',
        },
        'simple': {
            'format': '[{levelname}] {asctime} {message}',
            'style': '{',
            'datefmt': '%Y-%m-%d %H:%M:%S'
        },
    },
    'handlers': {
        'console': {
            'level': 'DEBUG',
            'class': 'logging.StreamHandler',
            'formatter': 'simple',
            'stream': 'ext://sys.stdout',
        },
    },
    'root': {
        'handlers': ['console'],
        'level': 'INFO',
    },
    'loggers': {
        'django': {
            'handlers': ['console'],
            'level': 'INFO',
            'propagate': False,
        },
        'django.request': {
            'handlers': ['console'],
            'level': 'DEBUG',
            'propagate': False,
        },
        'django.server': {
            'handlers': ['console'],
            'level': 'DEBUG',
            'propagate': False,
        },
        'django.db.backends': {
            'handlers': ['console'],
            'level': 'WARNING',  # Set to DEBUG to see SQL queries
            'propagate': False,
        },
        'claims': {
            'handlers': ['console'],
            'level': 'DEBUG',
            'propagate': False,
        },
        'documents': {
            'handlers': ['console'],
            'level': 'DEBUG',
            'propagate': False,
        },
        'payments': {
            'handlers': ['console'],
            'level': 'DEBUG',
            'propagate': False,
        },
        'users': {
            'handlers': ['console'],
            'level': 'DEBUG',
            'propagate': False,
        },
    },
}
