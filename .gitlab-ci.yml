# GitLab CI/CD Pipeline for IMT Insurance Claims System
# Builds Docker image and pushes to registry.imtins.com:5005

stages:
  - build
  - test
  - publish

variables:
  # Docker registry configuration
  REGISTRY_URL: "registry.imtins.com:5005"
  IMAGE_NAME: "carter.minear/demo-claims"
  
  # Docker-in-Docker configuration
  DOCKER_DRIVER: overlay2
  DOCKER_TLS_CERTDIR: ""
  DOCKER_HOST: tcp://docker:2375
  
  # Cache pip dependencies
  PIP_CACHE_DIR: "$CI_PROJECT_DIR/.cache/pip"

# Global before_script for Docker jobs
.docker_setup: &docker_setup
  - |
    # Wait for Docker daemon to be ready
    until docker info >/dev/null 2>&1; do
      echo "Waiting for Docker daemon to be ready..."
      sleep 3
    done
    echo "Docker daemon is ready"
  - echo "$CI_REGISTRY_PASSWORD" | docker login -u "$CI_REGISTRY_USER" --password-stdin $REGISTRY_URL



# Build stage
build:
  stage: build
  image: docker:24.0.5
  services:
    - docker:24.0.5-dind
  variables:
    DOCKER_TLS_CERTDIR: ""  # Disable TLS
  tags:
    - docker-tests
  before_script:
    - *docker_setup
  script:
    # Build the Docker image
    - |
      echo "🏗️ Building Docker image..."
      docker build \
        --tag $REGISTRY_URL/$IMAGE_NAME:$CI_COMMIT_SHA \
        --tag $REGISTRY_URL/$IMAGE_NAME:$CI_COMMIT_REF_SLUG \
        --tag $REGISTRY_URL/$IMAGE_NAME:latest \
        .
    
    # Push all tags
    - |
      echo "📤 Pushing images to registry..."
      docker push $REGISTRY_URL/$IMAGE_NAME:$CI_COMMIT_SHA
      docker push $REGISTRY_URL/$IMAGE_NAME:$CI_COMMIT_REF_SLUG
      if [ "$CI_COMMIT_BRANCH" = "$CI_DEFAULT_BRANCH" ]; then
        docker push $REGISTRY_URL/$IMAGE_NAME:latest
      fi
  rules:
    - if: '$CI_COMMIT_BRANCH'
    - if: '$CI_COMMIT_TAG'

# Test stage - run Django tests
test:unit:
  stage: test
  image: python:3.11-slim
  services:
    - postgres:15-alpine
    - redis:7-alpine
  variables:
    # Test database configuration
    POSTGRES_DB: test_claims_db
    POSTGRES_USER: test_user
    POSTGRES_PASSWORD: test_password
    POSTGRES_HOST_AUTH_METHOD: trust
    
    # Django test settings
    DJANGO_SETTINGS_MODULE: claims_system.settings.development
    DATABASE_URL: **************************************************/test_claims_db
    REDIS_URL: redis://redis:6379/1
    SECRET_KEY: test_secret_key_for_ci
    DEBUG: "False"
  cache:
    paths:
      - .cache/pip
  before_script:
    - apt-get update && apt-get install -y build-essential libpq-dev
    - pip install --upgrade pip
    - pip install -r requirements.txt
  script:
    - |
      echo "🧪 Running Django tests..."
      python manage.py migrate
      python manage.py test --verbosity=2
  coverage: '/TOTAL.*\s+(\d+%)$/'
  artifacts:
    reports:
      coverage_report:
        coverage_format: cobertura
        path: coverage.xml
    expire_in: 1 week
  rules:
    - if: '$CI_COMMIT_BRANCH'
    - if: '$CI_MERGE_REQUEST_ID'

# Security scanning
test:security:
  stage: test
  image: python:3.11-slim
  cache:
    paths:
      - .cache/pip
  before_script:
    - pip install --upgrade pip
    - pip install safety bandit
  script:
    - |
      echo "🔒 Running security scans..."
      safety check -r requirements.txt || true
      bandit -r . -f json -o bandit-report.json || true
  artifacts:
    reports:
      sast:
        - bandit-report.json
    expire_in: 1 week
  rules:
    - if: '$CI_COMMIT_BRANCH'
    - if: '$CI_MERGE_REQUEST_ID'

# Code quality checks
test:quality:
  stage: test
  image: python:3.11-slim
  cache:
    paths:
      - .cache/pip
  before_script:
    - pip install --upgrade pip
    - pip install flake8 pylint black isort
  script:
    - |
      echo "📝 Running code quality checks..."
      # Check code formatting
      black --check .
      isort --check-only .
      
      # Run linting
      flake8 . --count --select=E9,F63,F7,F82 --show-source --statistics
      flake8 . --count --exit-zero --max-complexity=10 --max-line-length=127 --statistics
  allow_failure: true
  rules:
    - if: '$CI_COMMIT_BRANCH'
    - if: '$CI_MERGE_REQUEST_ID'

# Publish production image (only on tags)
publish:production:
  stage: publish
  image: docker:24.0.5
  services:
    - docker:24.0.5-dind
  tags:
    - docker-tests
  before_script:
    - *docker_setup
  script:
    - |
      echo "🚀 Publishing production image..."
      # Pull the image built in the build stage
      docker pull $REGISTRY_URL/$IMAGE_NAME:$CI_COMMIT_SHA
      
      # Tag for production
      docker tag $REGISTRY_URL/$IMAGE_NAME:$CI_COMMIT_SHA $REGISTRY_URL/$IMAGE_NAME:$CI_COMMIT_TAG
      docker tag $REGISTRY_URL/$IMAGE_NAME:$CI_COMMIT_SHA $REGISTRY_URL/$IMAGE_NAME:stable
      
      # Push production tags
      docker push $REGISTRY_URL/$IMAGE_NAME:$CI_COMMIT_TAG
      docker push $REGISTRY_URL/$IMAGE_NAME:stable
      
      # Create release notes
      echo "📋 Image published:"
      echo "  - $REGISTRY_URL/$IMAGE_NAME:$CI_COMMIT_TAG"
      echo "  - $REGISTRY_URL/$IMAGE_NAME:stable"
  rules:
    - if: '$CI_COMMIT_TAG'

# Deploy to staging (optional - uncomment if you have staging environment)
# deploy:staging:
#   stage: deploy
#   image: bitnami/kubectl:latest
#   script:
#     - |
#       echo "🚢 Deploying to staging..."
#       # Update Kubernetes deployment
#       kubectl set image deployment/web web=$REGISTRY_URL/$IMAGE_NAME:$CI_COMMIT_SHA -n imt-staging
#       kubectl rollout status deployment/web -n imt-staging
#   environment:
#     name: staging
#     url: https://staging.imtins.com
#   rules:
#     - if: '$CI_COMMIT_BRANCH == $CI_DEFAULT_BRANCH'

# Create merge request image
build:mr:
  stage: build
  image: docker:24.0.5
  services:
    - docker:24.0.5-dind
  tags:
    - docker-tests
  before_script:
    - *docker_setup
  script:
    - |
      echo "🔨 Building merge request image..."
      docker build \
        --tag $REGISTRY_URL/$IMAGE_NAME:mr-$CI_MERGE_REQUEST_IID \
        .
      docker push $REGISTRY_URL/$IMAGE_NAME:mr-$CI_MERGE_REQUEST_IID
  rules:
    - if: '$CI_MERGE_REQUEST_ID'

# Cleanup old images (runs on schedule)
cleanup:
  stage: .post
  image: docker:24.0.5
  services:
    - docker:24.0.5-dind
  tags:
    - docker-tests
  before_script:
    - *docker_setup
  script:
    - |
      echo "🧹 Cleaning up old images..."
      # Add cleanup logic here based on your retention policy
      # Example: Remove images older than 30 days except stable/latest
  rules:
    - if: '$CI_PIPELINE_SOURCE == "schedule"'