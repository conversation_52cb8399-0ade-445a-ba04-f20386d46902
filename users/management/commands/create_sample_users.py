import random
from django.core.management.base import BaseCommand
from django.contrib.auth import get_user_model
from users.models import UserProfile

User = get_user_model()


class Command(BaseCommand):
    help = 'Create sample users for testing the claims system'

    def handle(self, *args, **options):
        # Create admin user
        admin_user, created = User.objects.get_or_create(
            username='admin',
            defaults={
                'email': '<EMAIL>',
                'first_name': 'Admin',
                'last_name': 'User',
                'user_type': 'admin',
                'is_staff': True,
                'is_superuser': True,
                'phone_number': '************',
                'address': '7825 Mills Civic Parkway, West Des Moines, IA 50266',
            }
        )
        if created:
            admin_user.set_password('admin123')
            admin_user.save()
            self.stdout.write(
                self.style.SUCCESS(f'Created admin user: {admin_user.username}')
            )
        else:
            self.stdout.write(f'Admin user already exists: {admin_user.username}')

        # Create adjuster user
        adjuster_user, created = User.objects.get_or_create(
            username='adjuster',
            defaults={
                'email': '<EMAIL>',
                'first_name': '<PERSON>',
                'last_name': '<PERSON>',
                'user_type': 'adjuster',
                'is_staff': True,
                'phone_number': '************',
                'address': '7825 Mills Civic Parkway, West Des Moines, IA 50266',
            }
        )
        if created:
            adjuster_user.set_password('adjuster123')
            adjuster_user.save()
            self.stdout.write(
                self.style.SUCCESS(f'Created adjuster user: {adjuster_user.username}')
            )
        else:
            self.stdout.write(f'Adjuster user already exists: {adjuster_user.username}')

        # Create customer user
        customer_user, created = User.objects.get_or_create(
            username='customer',
            defaults={
                'email': '<EMAIL>',
                'first_name': 'John',
                'last_name': 'Doe',
                'user_type': 'customer',
                'phone_number': '************',
                'address': '123 Main Street, Des Moines, IA 50309',
                'policy_number': 'POL-2024-001',
            }
        )
        if created:
            customer_user.set_password('customer123')
            customer_user.save()
            self.stdout.write(
                self.style.SUCCESS(f'Created customer user: {customer_user.username}')
            )
        else:
            self.stdout.write(f'Customer user already exists: {customer_user.username}')

        # Create agent user
        agent_user, created = User.objects.get_or_create(
            username='agent',
            defaults={
                'email': '<EMAIL>',
                'first_name': 'Mike',
                'last_name': 'Johnson',
                'user_type': 'agent',
                'phone_number': '************',
                'address': '456 Insurance Ave, Des Moines, IA 50309',
            }
        )
        if created:
            agent_user.set_password('agent123')
            agent_user.save()
            self.stdout.write(
                self.style.SUCCESS(f'Created agent user: {agent_user.username}')
            )
        else:
            self.stdout.write(f'Agent user already exists: {agent_user.username}')

        # Create user profiles if they don't exist
        for user in [admin_user, adjuster_user, customer_user, agent_user]:
            profile, created = UserProfile.objects.get_or_create(
                user=user,
                defaults={
                    'bio': f'Sample {user.get_user_type_display()} for IMT Insurance Claims System',
                    'preferred_communication': 'email',
                }
            )
            if created:
                self.stdout.write(f'Created profile for {user.username}')

        # Create specific users referenced in login template
        specific_users = [
            {
                'username': 'adjuster_barbara_rodriguez',
                'email': '<EMAIL>',
                'first_name': 'Barbara',
                'last_name': 'Rodriguez',
                'user_type': 'adjuster',
                'password': 'adjuster123',
                'is_staff': True,
            },
            {
                'username': 'adjuster_matthew_anderson',
                'email': '<EMAIL>',
                'first_name': 'Matthew',
                'last_name': 'Anderson',
                'user_type': 'adjuster',
                'password': 'adjuster123',
                'is_staff': True,
            },
            {
                'username': 'patricia.garcia44',
                'email': '<EMAIL>',
                'first_name': 'Patricia',
                'last_name': 'Garcia',
                'user_type': 'customer',
                'password': 'customer123',
                'policy_number': 'POL-2024-044',
            },
            {
                'username': 'nancy.ramirez10',
                'email': '<EMAIL>',
                'first_name': 'Nancy',
                'last_name': 'Ramirez',
                'user_type': 'customer',
                'password': 'customer123',
                'policy_number': 'POL-2024-010',
            },
            {
                'username': 'agent_betty_martin',
                'email': '<EMAIL>',
                'first_name': 'Betty',
                'last_name': 'Martin',
                'user_type': 'agent',
                'password': 'agent123',
            },
            {
                'username': 'agent_charles_white',
                'email': '<EMAIL>',
                'first_name': 'Charles',
                'last_name': 'White',
                'user_type': 'agent',
                'password': 'agent123',
            },
        ]

        for user_data in specific_users:
            username = user_data.pop('username')
            password = user_data.pop('password')

            specific_user, created = User.objects.get_or_create(
                username=username,
                defaults={
                    **user_data,
                    'phone_number': '515-555-' + str(random.randint(1000, 9999)),
                    'address': '7825 Mills Civic Parkway, West Des Moines, IA 50266',
                }
            )
            if created:
                specific_user.set_password(password)
                specific_user.save()
                self.stdout.write(
                    self.style.SUCCESS(f'Created specific user: {specific_user.username}')
                )
            else:
                self.stdout.write(f'Specific user already exists: {specific_user.username}')

            # Create profile for specific user
            profile, created = UserProfile.objects.get_or_create(
                user=specific_user,
                defaults={
                    'bio': f'Sample {specific_user.get_user_type_display()} for IMT Insurance Claims System',
                    'preferred_communication': 'email',
                }
            )
            if created:
                self.stdout.write(f'Created profile for {specific_user.username}')

        self.stdout.write(
            self.style.SUCCESS('\nSample users created successfully!')
        )
        self.stdout.write('Login credentials:')
        self.stdout.write('Admin: admin / admin123')
        self.stdout.write('Adjuster: adjuster / adjuster123')
        self.stdout.write('Customer: customer / customer123')
        self.stdout.write('Agent: agent / agent123')
        self.stdout.write('\nSpecific demo users:')
        self.stdout.write('Adjusters: adjuster_barbara_rodriguez, adjuster_matthew_anderson / adjuster123')
        self.stdout.write('Customers: patricia.garcia44, nancy.ramirez10 / customer123')
        self.stdout.write('Agents: agent_betty_martin, agent_charles_white / agent123')
