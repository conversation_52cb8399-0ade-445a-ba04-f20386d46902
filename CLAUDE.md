# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Commands

### Development
```bash
# Run development server
python manage.py runserver

# Run with Docker (development)
./deploy.sh docker

# Apply database migrations
python manage.py migrate

# Create superuser
python manage.py createsuperuser

# Seed database with sample data
python manage.py seed_database

# Create demo superuser (imtadmin/imtadmin123)
python manage.py create_demo_superuser

# Collect static files
python manage.py collectstatic

# Run tests
python manage.py test
python3 test_routes.py  # Route testing
```

### Docker Commands
```bash
# Build and deploy
./deploy.sh docker          # Development mode
./deploy.sh docker-ssl      # With SSL
./deploy.sh k8s            # Kubernetes deployment

# Monitoring
./deploy.sh logs           # Docker logs
./deploy.sh logs k8s       # Kubernetes logs

# Run Django commands in container
docker-compose exec web python manage.py <command>

# Database backup
docker-compose exec postgres pg_dump -U claims_user claims_db > backup.sql
```

### Celery Background Tasks
```bash
# Start Celery worker (handled automatically in Docker)
celery -A claims_system worker -l info

# Start Celery beat scheduler
celery -A claims_system beat -l info
```

## Architecture Overview

This is an IMT Insurance Claims Management System built with Django 4.2. The architecture follows Django's app-based structure with clear separation of concerns.

### Core Apps

1. **Users App**: Custom user model with role-based access (Admin, Claims Adjuster, Customer, Insurance Agent). Extended with UserProfile for additional fields like policy numbers and emergency contacts.

2. **Claims App**: Central to the system, manages insurance claims through their lifecycle (Submitted → Under Review → Investigating → Pending Documents → Approved/Denied → Closed). Supports multiple insurance types (Auto, Home, Business, Umbrella) with priority levels and auto-generated claim numbers (IMT-YYYY-XXXXXXXX).

3. **Documents App**: Handles file uploads with version control, permission-based access, and organized storage by claim/year. Integrates Dropzone.js for drag-and-drop uploads.

4. **Payments App**: Manages claim settlements and payments with full audit trail. Supports multiple payment methods and tracks payment status changes.

### Key Architectural Decisions

- **Settings Structure**: Split settings pattern (base.py, development.py, production.py) for environment-specific configurations
- **Database**: PostgreSQL in production, SQLite for development
- **Caching/Sessions**: Redis for both caching and Celery message broker
- **Static Files**: WhiteNoise for serving in production
- **Background Tasks**: Celery for async processing
- **Authentication**: Django's built-in auth with custom User model
- **API**: Django REST Framework with session-based authentication

### Request Flow

1. **Nginx** (production) → reverse proxy with SSL termination
2. **Gunicorn** → WSGI server running Django application
3. **Django** → handles requests, renders templates or API responses
4. **PostgreSQL** → persistent data storage
5. **Redis** → caching and Celery broker
6. **Celery Worker** → background task processing

### Key Models & Relationships

- User → Claims (one-to-many)
- Claim → Documents, ClaimNotes, Tasks, Payments (one-to-many)
- Claim → Settlement (one-to-one)
- Document → DocumentVersion (one-to-many)

### Template Structure

Templates use Bootstrap 5 with custom IMT styling. Base template provides consistent navigation and layout. Templates are organized by app (claims/, documents/, users/) with shared components in the root templates directory.

### Security Considerations

Production settings enforce:
- HTTPS/SSL redirect
- Secure cookies
- HSTS headers
- CSRF protection
- XSS prevention headers
- Content type sniffing prevention
- Role-based access control on views and API endpoints

### Deployment

The project supports multiple deployment strategies:
- **Docker Compose**: For local development and simple production setups
- **Kubernetes**: Full K8s manifests for scalable cloud deployment
- **Traditional**: Can run directly with Gunicorn + Nginx

Environment configuration uses python-decouple with `.env` files. See `.env.example` for all available settings.